import os
import j<PERSON>
from typing import Dict, Any, Optional
from openai import OpenAI
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class LLMClient:
    """Client for interacting with LLM API"""
    
    def __init__(self):
        self.api_key = os.getenv('OPENAI_API_KEY', '')
        self.base_url = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
        self.model = os.getenv('OPENAI_MODEL', 'gpt-4')
        
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY must be set in .env file")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
    
    def generate_workflow_modification(self, 
                                     current_workflow: Dict[str, Any], 
                                     user_request: str) -> Optional[Dict[str, Any]]:
        """Generate workflow modification based on user request"""
        
        # Create system prompt with n8n workflow context
        system_prompt = """You are an expert n8n workflow designer. Your task is to modify existing n8n workflows based on user requests.

Key guidelines:
1. Always preserve the existing workflow structure unless explicitly asked to change it
2. Add new nodes with proper connections
3. Use standard n8n node types (n8n-nodes-base.*)
4. Ensure proper node positioning (x, y coordinates)
5. Generate unique node IDs (UUIDs)
6. Maintain proper workflow JSON structure
7. Only modify what the user specifically requests

Common n8n node types:
- n8n-nodes-base.webhook (for HTTP triggers)
- n8n-nodes-base.httpRequest (for API calls)
- n8n-nodes-base.emailSend (for sending emails)
- n8n-nodes-base.set (for data manipulation)
- n8n-nodes-base.if (for conditional logic)
- n8n-nodes-base.code (for custom JavaScript)
- n8n-nodes-base.schedule (for time-based triggers)

Return ONLY the complete modified workflow JSON, no explanations."""

        user_prompt = f"""Current workflow:
{json.dumps(current_workflow, indent=2)}

User request: {user_request}

Please modify the workflow according to the user's request and return the complete updated workflow JSON."""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.1,
                max_tokens=4000
            )
            
            # Extract and parse the response
            content = response.choices[0].message.content.strip()
            
            # Try to extract JSON from the response
            if content.startswith('```json'):
                content = content[7:-3].strip()
            elif content.startswith('```'):
                content = content[3:-3].strip()
            
            try:
                modified_workflow = json.loads(content)
                return modified_workflow
            except json.JSONDecodeError as e:
                print(f"Failed to parse LLM response as JSON: {e}")
                print(f"Response content: {content}")
                return None
                
        except Exception as e:
            print(f"Error calling LLM API: {e}")
            return None
    
    def explain_workflow_changes(self, 
                               original_workflow: Dict[str, Any], 
                               modified_workflow: Dict[str, Any]) -> str:
        """Generate explanation of changes made to the workflow"""
        
        system_prompt = """You are an expert n8n workflow analyst. Compare two workflow versions and explain the changes in a clear, user-friendly way.

Focus on:
1. New nodes added
2. Nodes removed
3. Connections changed
4. Configuration changes
5. Overall workflow improvements

Use Czech language for the explanation."""

        user_prompt = f"""Original workflow:
{json.dumps(original_workflow, indent=2)}

Modified workflow:
{json.dumps(modified_workflow, indent=2)}

Please explain what changes were made to the workflow in Czech language."""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"Error explaining changes: {e}")
            return "Nepodařilo se vygenerovat vysvětlení změn."
