import json
import uuid
from typing import Dict, Any, List, Optional
from llm_client import LLMClient

class WorkflowGenerator:
    """AI-powered workflow generator and modifier"""
    
    def __init__(self):
        self.llm_client = LLMClient()
    
    def modify_workflow(self, 
                       current_workflow: Dict[str, Any], 
                       user_request: str) -> Optional[Dict[str, Any]]:
        """Modify workflow based on user request using AI"""
        
        # Validate current workflow
        if not self._validate_workflow(current_workflow):
            return None
        
        # Generate modification using LLM
        modified_workflow = self.llm_client.generate_workflow_modification(
            current_workflow, user_request
        )
        
        if not modified_workflow:
            return None
        
        # Post-process and validate the generated workflow
        processed_workflow = self._post_process_workflow(modified_workflow)
        
        if self._validate_workflow(processed_workflow):
            return processed_workflow
        else:
            return None
    
    def explain_changes(self, 
                       original: Dict[str, Any], 
                       modified: Dict[str, Any]) -> str:
        """Generate explanation of workflow changes"""
        return self.llm_client.explain_workflow_changes(original, modified)
    
    def _validate_workflow(self, workflow: Dict[str, Any]) -> bool:
        """Validate workflow structure"""
        required_fields = ['name', 'nodes', 'connections', 'settings']
        
        for field in required_fields:
            if field not in workflow:
                print(f"Missing required field: {field}")
                return False
        
        # Validate nodes
        if not isinstance(workflow['nodes'], list):
            print("Nodes must be a list")
            return False
        
        for node in workflow['nodes']:
            if not self._validate_node(node):
                return False
        
        # Validate connections
        if not isinstance(workflow['connections'], dict):
            print("Connections must be a dict")
            return False
        
        return True
    
    def _validate_node(self, node: Dict[str, Any]) -> bool:
        """Validate individual node structure"""
        required_fields = ['id', 'name', 'type', 'position', 'parameters']
        
        for field in required_fields:
            if field not in node:
                print(f"Node missing required field: {field}")
                return False
        
        # Validate position
        if not isinstance(node['position'], list) or len(node['position']) != 2:
            print("Node position must be [x, y] array")
            return False
        
        # Validate node type
        if not isinstance(node['type'], str) or not node['type']:
            print("Node type must be a non-empty string")
            return False
        
        return True
    
    def _post_process_workflow(self, workflow: Dict[str, Any]) -> Dict[str, Any]:
        """Post-process generated workflow to ensure consistency"""
        
        # Ensure all nodes have unique IDs
        used_ids = set()
        for node in workflow.get('nodes', []):
            if node.get('id') in used_ids:
                node['id'] = str(uuid.uuid4())
            used_ids.add(node['id'])
        
        # Ensure proper node positioning
        self._fix_node_positions(workflow)
        
        # Ensure proper typeVersion for nodes
        for node in workflow.get('nodes', []):
            if 'typeVersion' not in node:
                node['typeVersion'] = 1
        
        # Ensure workflow has proper settings
        if 'settings' not in workflow:
            workflow['settings'] = {}
        
        return workflow
    
    def _fix_node_positions(self, workflow: Dict[str, Any]):
        """Fix node positions to avoid overlaps"""
        nodes = workflow.get('nodes', [])
        
        # Simple positioning algorithm - arrange nodes in a grid
        x_offset = 0
        y_offset = 0
        nodes_per_row = 3
        node_spacing_x = 300
        node_spacing_y = 200
        
        for i, node in enumerate(nodes):
            if i > 0 and i % nodes_per_row == 0:
                y_offset += node_spacing_y
                x_offset = 0
            
            node['position'] = [x_offset, y_offset]
            x_offset += node_spacing_x
    
    def get_common_node_templates(self) -> Dict[str, Dict[str, Any]]:
        """Get templates for common n8n nodes"""
        return {
            'webhook': {
                'type': 'n8n-nodes-base.webhook',
                'parameters': {
                    'httpMethod': 'POST',
                    'path': 'webhook',
                    'responseMode': 'onReceived'
                }
            },
            'http_request': {
                'type': 'n8n-nodes-base.httpRequest',
                'parameters': {
                    'method': 'GET',
                    'url': ''
                }
            },
            'email_send': {
                'type': 'n8n-nodes-base.emailSend',
                'parameters': {
                    'fromEmail': '',
                    'toEmail': '',
                    'subject': '',
                    'text': ''
                }
            },
            'set': {
                'type': 'n8n-nodes-base.set',
                'parameters': {
                    'values': {
                        'string': []
                    }
                }
            },
            'if': {
                'type': 'n8n-nodes-base.if',
                'parameters': {
                    'conditions': {
                        'string': []
                    }
                }
            },
            'code': {
                'type': 'n8n-nodes-base.code',
                'parameters': {
                    'jsCode': '// Add your JavaScript code here\nreturn items;'
                }
            }
        }
    
    def create_sample_workflow(self, name: str = "Sample Workflow") -> Dict[str, Any]:
        """Create a sample workflow for testing"""
        return {
            "name": name,
            "nodes": [
                {
                    "id": str(uuid.uuid4()),
                    "name": "Webhook",
                    "type": "n8n-nodes-base.webhook",
                    "typeVersion": 1,
                    "position": [0, 0],
                    "parameters": {
                        "httpMethod": "POST",
                        "path": "sample",
                        "responseMode": "onReceived"
                    }
                }
            ],
            "connections": {},
            "settings": {
                "saveExecutionProgress": False,
                "saveManualExecutions": False,
                "saveDataErrorExecution": "all",
                "saveDataSuccessExecution": "all"
            },
            "staticData": {},
            "active": False
        }
