# n8n Workflow Manager

Streamlit aplikace pro správu n8n workflow přes API.

## 🚀 Spuštění

1. **Instalace závislostí:**
```bash
pip install -r requirements.txt
```

2. **Konfigurace:**
<PERSON><PERSON><PERSON><PERSON><PERSON> se, že máte správn<PERSON> nasta<PERSON> `.env` soubor:
```
N8N_HOSTNAME="https://n8n.lsoffice.cz/"
N8N_API_KEY="your_api_key_here"
```

3. **Spuštění aplikace:**
```bash
streamlit run app.py
```

## 📋 Funkcionalita

- **Načtení workflow** z n8n pomocí URL
- **Zobrazení informací** o workflow
- **Ruční úprava** workflow JSON
- **Aktualizace workflow** přes API
- **Validace URL** a error handling

## 🔧 Použití

1. Zadejte URL vašeho n8n workflow (např. `https://n8n.lsoffice.cz/workflow/YDos2Kp8qDMKZDWf`)
2. Klikněte na "Načíst workflow"
3. Prohlédněte si informace o workflow
4. Upravte JSON podle potřeby
5. Klikněte na "Aktualizovat z JSON"

## 📁 Struktura

- `app.py` - Hlavní Streamlit aplikace
- `n8n_client.py` - n8n API klient
- `workflow_parser.py` - Parser pro workflow URL a data
- `.env` - Konfigurace serveru (hostname, API key)
