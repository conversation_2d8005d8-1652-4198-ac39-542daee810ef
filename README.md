# n8n Workflow Manager

Streamlit aplikace pro správu n8n workflow přes API s AI asistovanou úpravou.

## 🚀 Spuštění

1. **Instalace závislostí:**
```bash
pip install -r requirements.txt
```

2. **Konfigurace:**
Ujistěte se, že máte správně nastavený `.env` soubor:
```
N8N_HOSTNAME="https://n8n.lsoffice.cz/"
N8N_API_KEY="your_api_key_here"
OPENAI_API_KEY="your_openai_api_key"
OPENAI_BASE_URL="https://llm-proxy-ai.kube1-tt2.lskube.eu/v1/"
OPENAI_MODEL="gpt-4.1-mini-2025-04-14"
```

3. **Spuštění aplikace:**
```bash
streamlit run app.py
```

## 🤖 Nové AI Funkce

- **AI asistovaná úprava** - Popište změny v přirozeném jazyce
- **Automatické generování** workflow podle popisu
- **Vysvětlení změn** - AI vysvětlí, co bylo změněno
- **Náhled změn** před aplikací
- **Validace workflow** - Kontrola správnosti vygenerovaného workflow

## 📋 Funkcionalita

- **Načtení workflow** z n8n pomocí URL
- **Zobrazení informací** o workflow
- **🆕 AI úprava workflow** podle textového popisu
- **Ruční úprava** workflow JSON
- **Aktualizace workflow** přes API
- **Validace URL** a error handling

## 🔧 Použití

### Základní použití:
1. Zadejte URL vašeho n8n workflow (např. `https://n8n.lsoffice.cz/workflow/YDos2Kp8qDMKZDWf`)
2. Klikněte na "Načíst workflow"
3. Prohlédněte si informace o workflow

### AI úprava workflow:
4. Popište požadované změny v textovém poli (např. "Přidej webhook trigger a email node")
5. Klikněte na "🧠 Vygenerovat úpravu"
6. Prohlédněte si náhled změn a vysvětlení
7. Klikněte na "💾 Aplikovat změny"

### Ruční úprava:
4. Rozbalte "🛠️ Ruční úprava JSON"
5. Upravte JSON podle potřeby
6. Klikněte na "💾 Aktualizovat z JSON"

## 📁 Struktura

- `app.py` - Hlavní Streamlit aplikace
- `n8n_client.py` - n8n API klient
- `workflow_parser.py` - Parser pro workflow URL a data
- `llm_client.py` - LLM API klient pro AI funkce
- `workflow_generator.py` - AI generátor workflow
- `.env` - Konfigurace serveru (hostname, API key, LLM)
