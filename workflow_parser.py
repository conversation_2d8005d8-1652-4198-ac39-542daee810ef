import re
from urllib.parse import urlparse
from typing import Optional, Dict, Any

class WorkflowParser:
    """Parser for n8n workflow URLs and data"""
    
    @staticmethod
    def extract_workflow_id(url: str) -> Optional[str]:
        """Extract workflow ID from n8n URL"""
        # Pattern for n8n workflow URLs: https://domain/workflow/ID
        pattern = r'/workflow/([a-zA-Z0-9]+)'
        match = re.search(pattern, url)
        return match.group(1) if match else None
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """Validate if URL is a proper n8n workflow URL"""
        try:
            parsed = urlparse(url)
            return (
                parsed.scheme in ['http', 'https'] and
                parsed.netloc and
                '/workflow/' in parsed.path
            )
        except Exception:
            return False
    
    @staticmethod
    def format_workflow_info(workflow_data: Dict[str, Any]) -> str:
        """Format workflow data for display"""
        if not workflow_data:
            return "No workflow data available"
        
        info = []
        info.append(f"**Name:** {workflow_data.get('name', 'Unnamed')}")
        info.append(f"**ID:** {workflow_data.get('id', 'Unknown')}")
        info.append(f"**Active:** {'Yes' if workflow_data.get('active', False) else 'No'}")
        
        nodes = workflow_data.get('nodes', [])
        info.append(f"**Nodes:** {len(nodes)}")
        
        if nodes:
            node_types = [node.get('type', 'Unknown') for node in nodes]
            unique_types = list(set(node_types))
            info.append(f"**Node Types:** {', '.join(unique_types)}")
        
        connections = workflow_data.get('connections', {})
        info.append(f"**Connections:** {len(connections)}")
        
        return "\n".join(info)
    
    @staticmethod
    def get_workflow_summary(workflow_data: Dict[str, Any]) -> str:
        """Get a brief summary of the workflow"""
        if not workflow_data:
            return "No workflow data"
        
        name = workflow_data.get('name', 'Unnamed Workflow')
        nodes = workflow_data.get('nodes', [])
        node_count = len(nodes)
        
        if node_count == 0:
            return f"{name} (empty workflow)"
        
        # Get first and last nodes for basic flow description
        trigger_nodes = [n for n in nodes if n.get('type', '').endswith('Trigger')]
        action_nodes = [n for n in nodes if not n.get('type', '').endswith('Trigger')]
        
        summary = f"{name} ({node_count} nodes"
        if trigger_nodes:
            summary += f", {len(trigger_nodes)} triggers"
        if action_nodes:
            summary += f", {len(action_nodes)} actions"
        summary += ")"
        
        return summary
