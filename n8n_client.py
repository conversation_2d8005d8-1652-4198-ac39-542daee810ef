import requests
import os
from typing import Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class N8NClient:
    """Client for interacting with n8n API"""
    
    def __init__(self):
        self.hostname = os.getenv('N8N_HOSTNAME', '').rstrip('/')
        self.api_key = os.getenv('N8N_API_KEY', '')
        
        if not self.hostname or not self.api_key:
            raise ValueError("N8N_HOSTNAME and N8N_API_KEY must be set in .env file")
        
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
    
    def get_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get workflow by ID"""
        try:
            url = f"{self.hostname}/api/v1/workflows/{workflow_id}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error fetching workflow: {e}")
            return None
    
    def update_workflow(self, workflow_id: str, workflow_data: Dict[str, Any]) -> bool:
        """Update workflow with new data"""
        try:
            url = f"{self.hostname}/api/v1/workflows/{workflow_id}"
            response = requests.put(url, headers=self.headers, json=workflow_data)
            response.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            print(f"Error updating workflow: {e}")
            return False
    
    def list_workflows(self) -> Optional[Dict[str, Any]]:
        """List all workflows"""
        try:
            url = f"{self.hostname}/api/v1/workflows"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error listing workflows: {e}")
            return None
