import streamlit as st
import json
from n8n_client import N8<PERSON>lient
from workflow_parser import WorkflowParser

# Page configuration
st.set_page_config(
    page_title="n8n Workflow Manager",
    page_icon="🔄",
    layout="wide"
)

# Initialize session state
if 'workflow_data' not in st.session_state:
    st.session_state.workflow_data = None
if 'workflow_id' not in st.session_state:
    st.session_state.workflow_id = None

def main():
    st.title("🔄 n8n Workflow Manager")
    st.markdown("Spravujte své n8n workflow jednoduše přes API")
    
    # Initialize clients
    try:
        n8n_client = N8NClient()
        parser = WorkflowParser()
    except ValueError as e:
        st.error(f"Chyba konfigurace: {e}")
        st.stop()
    
    # Sidebar for workflow URL input
    with st.sidebar:
        st.header("📝 Workflow URL")
        workflow_url = st.text_input(
            "Zadejte URL workflow:",
            placeholder="https://n8n.lsoffice.cz/workflow/YDos2Kp8qDMKZDWf",
            help="Vložte odkaz na váš n8n workflow"
        )
        
        if workflow_url:
            if parser.validate_url(workflow_url):
                workflow_id = parser.extract_workflow_id(workflow_url)
                if workflow_id:
                    st.success(f"✅ Workflow ID: {workflow_id}")
                    st.session_state.workflow_id = workflow_id
                else:
                    st.error("❌ Nepodařilo se extrahovat workflow ID")
            else:
                st.error("❌ Neplatná URL")
        
        # Load workflow button
        if st.button("📥 Načíst workflow", disabled=not st.session_state.workflow_id):
            with st.spinner("Načítám workflow..."):
                workflow_data = n8n_client.get_workflow(st.session_state.workflow_id)
                if workflow_data:
                    st.session_state.workflow_data = workflow_data
                    st.success("✅ Workflow načten!")
                else:
                    st.error("❌ Nepodařilo se načíst workflow")
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("📊 Současný workflow")
        
        if st.session_state.workflow_data:
            # Display workflow info
            workflow_info = parser.format_workflow_info(st.session_state.workflow_data)
            st.markdown(workflow_info)
            
            # Show workflow JSON in expander
            with st.expander("🔍 Zobrazit JSON data"):
                st.json(st.session_state.workflow_data)
        else:
            st.info("👆 Nejdříve načtěte workflow pomocí URL v postranním panelu")
    
    with col2:
        st.header("✏️ Úprava workflow")
        
        if st.session_state.workflow_data:
            # Workflow description input
            workflow_description = st.text_area(
                "Popište, jak chcete workflow upravit:",
                height=200,
                placeholder="Například: Přidej webhook trigger, který při příchodu dat pošle email..."
            )
            
            # Manual JSON editing option
            with st.expander("🛠️ Ruční úprava JSON"):
                edited_json = st.text_area(
                    "JSON workflow:",
                    value=json.dumps(st.session_state.workflow_data, indent=2),
                    height=300
                )
                
                if st.button("💾 Aktualizovat z JSON"):
                    try:
                        updated_data = json.loads(edited_json)
                        with st.spinner("Aktualizuji workflow..."):
                            success = n8n_client.update_workflow(
                                st.session_state.workflow_id, 
                                updated_data
                            )
                            if success:
                                st.success("✅ Workflow úspěšně aktualizován!")
                                st.session_state.workflow_data = updated_data
                            else:
                                st.error("❌ Nepodařilo se aktualizovat workflow")
                    except json.JSONDecodeError:
                        st.error("❌ Neplatný JSON formát")
            
            # AI-assisted workflow modification (placeholder for future implementation)
            st.markdown("---")
            st.markdown("🤖 **AI asistovaná úprava** (připravuje se)")
            st.info("V budoucí verzi zde bude možnost popsat změny v přirozeném jazyce a nechat AI upravit workflow automaticky.")
            
        else:
            st.info("👈 Nejdříve načtěte workflow")

if __name__ == "__main__":
    main()
